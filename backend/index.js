const express = require('express');
const cors = require('cors');
const path = require('path');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const session = require('express-session');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');

// Passport configuration
const passport = require('./lib/config/passport');

// Handle routes
const ApiRoutes = require('./lib/routes/api');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').createServer(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));

// Session configuration for OAuth
if (config.get('oauth.enabled')) {
  app.use(session({
    secret: config.get('session.secret'),
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // Set to true in production with HTTPS
      maxAge: config.get('session.maxAge')
    }
  }));

  // Initialize Passport
  app.use(passport.initialize());
  app.use(passport.session());
}

// Disable caching for static files - DISABLED (frontend container serves React app)
// app.use(express.static('public', {
//   setHeaders: (res, path) => {
//     res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//     res.setHeader('Pragma', 'no-cache');
//     res.setHeader('Expires', '0');
//   }
// }));

// Force serve the correct React app - DISABLED (frontend container serves React app)
// app.get('/', (req, res) => {
//   res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//   res.setHeader('Pragma', 'no-cache');
//   res.setHeader('Expires', '0');
//   res.sendFile(path.join(__dirname, 'public', 'index.html'));
// });

// Test route to serve React app
app.get('/app', (req, res) => {
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.sendFile(path.join(__dirname, 'public', 'react-app.html'));
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  next();
});

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// Import upload middleware
const { uploadSingle, handleUploadError } = require('./lib/middleware/upload');

// API Routes - Example routes for the template
try {
  console.log('Registering routes...');
  declareRoute('post', '/auth/login', [], ApiRoutes.auth.login);
  declareRoute('post', '/auth/register', [], ApiRoutes.auth.register);
  declareRoute('post', '/auth/logout', [tokenToUserMiddleware], ApiRoutes.auth.logout);
  declareRoute('post', '/auth/forgot-password', [], ApiRoutes.auth.forgotPassword);
  declareRoute('post', '/auth/reset-password', [], ApiRoutes.auth.resetPassword);
  declareRoute('get', '/auth/verify-email', [], ApiRoutes.auth.verifyEmail);
  declareRoute('post', '/auth/resend-verification', [], ApiRoutes.auth.resendVerification);

  // OAuth config route
  declareRoute('get', '/auth/oauth/config', [], ApiRoutes.auth.oauth.config);

  // OAuth routes
  if (config.get('oauth.enabled')) {
    if (config.get('oauth.google.enabled')) {
      app.get('/auth/google', ApiRoutes.auth.oauth.google['v1.0'].login);
      app.get('/auth/google/callback', ApiRoutes.auth.oauth.google['v1.0'].callback);
    }
    if (config.get('oauth.telegram.enabled')) {
      app.get('/auth/telegram', ApiRoutes.auth.oauth.telegram['v1.0'].login);
      app.get('/auth/telegram/callback', ApiRoutes.auth.oauth.telegram['v1.0'].callback);
      declareRoute('post', '/auth/telegram/verify', [], ApiRoutes.auth.oauth.telegram['v1.0'].verifyWidget);
    }
  }

  declareRoute('post', '/user/profile', [tokenToUserMiddleware], ApiRoutes.user.profile);
  declareRoute('post', '/user/update', [tokenToUserMiddleware], ApiRoutes.user.update);
  declareRoute('get', '/user/storage', [tokenToUserMiddleware], ApiRoutes.user.storage);
  declareRoute('post', '/user/storage-sync', [tokenToUserMiddleware], ApiRoutes.user.storageSync);

  // File management routes (require authentication)
  declareRoute('post', '/files/upload', [tokenToUserMiddleware, uploadSingle, handleUploadError], ApiRoutes.files.upload);
  declareRoute('get', '/files/download/:fileId', [tokenToUserMiddleware], ApiRoutes.files.download);
  declareRoute('get', '/files/preview/:fileId', [tokenToUserMiddleware], ApiRoutes.files.preview);
  declareRoute('get', '/files/info/:fileId', [tokenToUserMiddleware], ApiRoutes.files.info);
  console.log('About to register browse route...');
  declareRoute('get', '/browse/:folderId?', [tokenToUserMiddleware], ApiRoutes.browse);
  console.log('Browse route registered successfully');

  // Folder routes (require authentication)
  declareRoute('post', '/folders/create', [tokenToUserMiddleware], ApiRoutes.folders.create);
  declareRoute('post', '/folders', [tokenToUserMiddleware], ApiRoutes.folders.create); // Alias for frontend compatibility
  declareRoute('get', '/folders/tree', [tokenToUserMiddleware], ApiRoutes.folders.tree);
  console.log('Folder routes registered successfully');
  console.log('All routes registered successfully');
} catch (error) {
  console.error('Error registering routes:', error);
}

declareRoute('put', '/items/:id/rename', [tokenToUserMiddleware], ApiRoutes.items.rename);
declareRoute('delete', '/items/:id', [tokenToUserMiddleware], ApiRoutes.items.delete);
declareRoute('post', '/items/move/:id', [tokenToUserMiddleware], ApiRoutes.items.move);
declareRoute('post', '/items/bulk-move', [tokenToUserMiddleware], ApiRoutes.items.bulkMove);
declareRoute('get', '/search', [tokenToUserMiddleware], ApiRoutes.search);

// Serve static files for login success page
app.get('/login-success', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Login Success - TeleStore</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          margin: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
        .container { text-align: center; }
        .spinner {
          width: 50px;
          height: 50px;
          border: 3px solid rgba(255,255,255,0.3);
          border-top: 3px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="spinner"></div>
        <p>Completing login...</p>
        <script>
          const urlParams = new URLSearchParams(window.location.search);
          const token = urlParams.get('token');
          const user = urlParams.get('user');

          if (token && user) {
            localStorage.setItem('token', token);
            localStorage.setItem('user', user);
            window.location.href = '/';
          } else {
            window.location.href = '/login?error=missing_auth_data';
          }
        </script>
      </div>
    </body>
    </html>
  `);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

const port = _.get(config, 'port', 3000);
console.log('About to start server on port:', port);
console.log('Express app routes:');
app._router.stack.forEach((middleware, index) => {
  if (middleware.route) {
    console.log(`${index}: ${middleware.route.stack[0].method.toUpperCase()} ${middleware.route.path}`);
  } else if (middleware.name === 'router') {
    console.log(`${index}: Router middleware`);
  } else {
    console.log(`${index}: ${middleware.name} middleware`);
  }
});

server.listen(port, '0.0.0.0', () => {
  logger.logInfo('Server listening at port:', port);
  console.log('Server started successfully!');
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
