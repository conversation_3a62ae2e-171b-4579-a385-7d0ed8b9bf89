import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
} from '@mui/material';
import {
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { FolderItem, Item } from '../types';
import FolderTreeView from './FolderTreeView';

interface MoveDialogProps {
  open: boolean;
  onClose: () => void;
  onMove: (destinationFolderId: string | null) => void;
  folders: FolderItem[];
  excludeId?: string; // Để loại trừ folder hiện tại và con của nó
  currentFolderId?: string | null; // Folder hiện tại để highlight
  isMovingFolder?: boolean; // true if moving a folder, false if moving a file
  itemToMove?: Item | null;
}

const MoveDialog: React.FC<MoveDialogProps> = ({
  open,
  onClose,
  onMove,
  folders,
  excludeId,
  currentFolderId,
  isMovingFolder = false,
  itemToMove
}) => {
  const { t } = useTranslation();
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);

  // Reset selection when dialog opens
  React.useEffect(() => {
    if (open) {
      setSelectedFolderId(null);
    }
  }, [open]);

  const handleFolderSelect = (folderId: string | null) => {
    setSelectedFolderId(folderId);
  };

  const handleConfirmMove = () => {
    if (selectedFolderId !== null) {
      onMove(selectedFolderId);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FolderOpenIcon color="primary" />
          {t('moveDialog.title', 'Move to Folder')}
        </Box>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
          {t('moveDialog.selectDestination', 'Select a destination folder:')}
        </Typography>

        <FolderTreeView
          selectedFolderId={selectedFolderId}
          onFolderSelect={handleFolderSelect}
          excludeId={excludeId}
          currentFolderId={currentFolderId}
          isMovingFolder={isMovingFolder}
        />
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} color="inherit">
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleConfirmMove}
          disabled={selectedFolderId === null}
          variant="contained"
        >
          {t('moveDialog.move', 'Move')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MoveDialog;