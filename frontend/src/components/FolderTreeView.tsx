import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Collapse,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  useTheme
} from '@mui/material';
import {
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Home as HomeIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { FolderTreeNode } from '../types';
import { fileApi } from '../services/api';

interface FolderTreeViewProps {
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string | null) => void;
  excludeId?: string;
  currentFolderId?: string | null;
  isMovingFolder?: boolean; // true if moving a folder, false if moving a file
}

interface TreeItemProps {
  node: FolderTreeNode;
  level: number;
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string | null) => void;
  excludeId?: string;
  currentFolderId?: string | null;
  expandedNodes: Set<string>;
  onToggleExpand: (nodeId: string) => void;
}

const TreeItem: React.FC<TreeItemProps> = ({
  node,
  level,
  selectedFolderId,
  onFolderSelect,
  excludeId,
  currentFolderId,
  expandedNodes,
  onToggleExpand,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const isExpanded = expandedNodes.has(node.id || 'root');
  const isSelected = selectedFolderId ? (selectedFolderId === node.id || (selectedFolderId === 'root' && node.id === null)) : false;
  const isCurrentLocation = currentFolderId === node.id;
  const isDisabled = node.isDisabled || isCurrentLocation;

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.hasChildren) {
      onToggleExpand(node.id || 'root');
    }
  };

  const handleSelect = () => {
    if (!isDisabled) {
      onFolderSelect(node.id || 'root');
    } else {

    }
  };

  const getIcon = () => {
    if (node.isRoot) {
      return <HomeIcon color={isSelected ? 'primary' : 'inherit'} />;
    }
    return isExpanded ? (
      <FolderOpenIcon color={isSelected ? 'primary' : 'inherit'} />
    ) : (
      <FolderIcon color={isSelected ? 'primary' : 'inherit'} />
    );
  };

  const getExpandIcon = () => {
    if (!node.hasChildren) return null;
    return isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />;
  };

  return (
    <>
      <ListItemButton
        selected={isSelected}
        onClick={handleSelect}
        // disabled={isDisabled}
        sx={{
          pl: level * 2 + 1,
          borderRadius: 1,
          mb: 0.5,
          '&.Mui-selected': {
            backgroundColor: theme.palette.primary.main + "20"
          },
          '&.Mui-disabled': {
            opacity: 0.6,
          },
        }}
      >
        {node.hasChildren && (
          <IconButton
            size="small"
            onClick={handleToggleExpand}
            sx={{ mr: 0.5, p: 0.5 }}
          >
            {getExpandIcon()}
          </IconButton>
        )}
        {!node.hasChildren && <Box sx={{ width: 32, mr: 0.5 }} />}

        <ListItemIcon sx={{ minWidth: 36 }}>
          {getIcon()}
        </ListItemIcon>

        <ListItemText
          primary={node.name}
          secondary={
            isCurrentLocation
              ? t('moveDialog.current', 'Current')
              : node.isDisabled
              ? t('moveDialog.disabled', 'Cannot move here')
              : node.isRoot
              ? t('moveDialog.rootDescription', 'Move to the main directory')
              : undefined
          }
          primaryTypographyProps={{
            fontSize: '0.875rem',
            fontWeight: isSelected ? 600 : 400,
            color: isSelected ? 'primary.main' : 'text.primary',
          }}
          secondaryTypographyProps={{
            fontSize: '0.75rem',
            color: 'text.secondary',
          }}
        />
      </ListItemButton>

      {node.hasChildren && (
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {node.children.map((child) => (
                <TreeItem
                  key={child.id || 'child-root'}
                  node={child}
                  level={level + 1}
                  selectedFolderId={selectedFolderId}
                  onFolderSelect={onFolderSelect}
                  excludeId={excludeId}
                  currentFolderId={currentFolderId}
                  expandedNodes={expandedNodes}
                  onToggleExpand={onToggleExpand}
                />
              ))}
          </List>
        </Collapse>
      )}
    </>
  );
};

const FolderTreeView: React.FC<FolderTreeViewProps> = ({
  selectedFolderId,
  onFolderSelect,
  excludeId,
  currentFolderId,
  isMovingFolder = false,
}) => {
  const { t } = useTranslation();
  const [folderTree, setFolderTree] = useState<FolderTreeNode | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));

  useEffect(() => {
    loadFolderTree();
  }, [excludeId, isMovingFolder]);

  const loadFolderTree = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fileApi.getFolderTree(excludeId, isMovingFolder);

      if (response.code === 200 && response.data?.tree) {
        setFolderTree(response.data.tree);
        // Auto-expand root
        setExpandedNodes(new Set(['root']));
      } else {
        setError('Failed to load folder tree');
      }
    } catch (err: any) {
      console.error('Error loading folder tree:', err);
      setError(err.message || 'Failed to load folder tree');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleExpand = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
        <CircularProgress size={24} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          {t('moveDialog.loadingFolders', 'Loading folders...')}
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!folderTree) {
    return (
      <Box sx={{ textAlign: 'center', py: 3, color: 'text.secondary' }}>
        <FolderIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />
        <Typography variant="body2">
          {t('moveDialog.noFolders', 'No folders available')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
      <List dense>
        <TreeItem
          node={folderTree}
          level={0}
          selectedFolderId={selectedFolderId}
          onFolderSelect={onFolderSelect}
          excludeId={excludeId}
          currentFolderId={currentFolderId}
          expandedNodes={expandedNodes}
          onToggleExpand={handleToggleExpand}
        />
      </List>
    </Box>
  );
};

export default FolderTreeView;
