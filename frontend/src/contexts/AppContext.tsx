import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Item, BreadcrumbItem, UploadProgress } from '../types';
import { useTranslation } from 'react-i18next';

interface AppState {
  currentFolderId: string | null;
  items: Item[];
  breadcrumbs: BreadcrumbItem[];
  loading: boolean;
  error: string | null;
  uploads: UploadProgress[];
  searchQuery: string;
  searchResults: Item[];
  isSearching: boolean;
}

type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURRENT_FOLDER'; payload: string | null }
  | { type: 'SET_ITEMS'; payload: Item[] }
  | { type: 'SET_BREADCRUMBS'; payload: BreadcrumbItem[] }
  | { type: 'ADD_UPLOAD'; payload: UploadProgress }
  | { type: 'UPDATE_UPLOAD'; payload: { fileName: string; progress: number; status: UploadProgress['status'] } }
  | { type: 'REMOVE_UPLOAD'; payload: string }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SEARCH_RESULTS'; payload: Item[] }
  | { type: 'SET_SEARCHING'; payload: boolean }
  | { type: 'CLEAR_SEARCH' };

const initialState: AppState = {
  currentFolderId: null,
  items: [],
  breadcrumbs: [], // Sẽ khởi tạo trong AppProvider
  loading: false,
  error: null,
  uploads: [],
  searchQuery: '',
  searchResults: [],
  isSearching: false,
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_CURRENT_FOLDER':
      return { ...state, currentFolderId: action.payload };
    case 'SET_ITEMS':
      return { ...state, items: action.payload };
    case 'SET_BREADCRUMBS':
      return { ...state, breadcrumbs: action.payload };
    case 'ADD_UPLOAD':
      return { ...state, uploads: [...state.uploads, action.payload] };
    case 'UPDATE_UPLOAD':
      return {
        ...state,
        uploads: state.uploads.map(upload =>
          upload.fileName === action.payload.fileName
            ? { ...upload, progress: action.payload.progress, status: action.payload.status }
            : upload
        ),
      };
    case 'REMOVE_UPLOAD':
      return {
        ...state,
        uploads: state.uploads.filter(upload => upload.fileName !== action.payload),
      };
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_SEARCH_RESULTS':
      return { ...state, searchResults: action.payload };
    case 'SET_SEARCHING':
      return { ...state, isSearching: action.payload };
    case 'CLEAR_SEARCH':
      return { ...state, searchQuery: '', searchResults: [], isSearching: false };
    default:
      return state;
  }
};

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useTranslation();
  const [state, dispatch] = React.useReducer(appReducer, {
    ...initialState,
    breadcrumbs: [{ id: null, name: t('sidebar.home') }],
  });

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
